<?php
/**
 * Plugin Name: Q-Editor
 * Description: Adds ACE Editor with Live Preview (processing shortcodes) to the Formidable Views editor.
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL2
 * Text Domain: q-editor
 */

if (!defined('ABSPATH'))
    exit;

/**
 * Enqueue Q-Editor scripts and styles in the admin.
 */
function qeditor_enqueue_admin_assets($hook)
{
    // Only load on post editing screens (where Formidable Views editor lives).
    if (in_array($hook, ['post-new.php', 'post.php'])) {
        // Add ajaxurl global.
        ?>
        <script>var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';</script>
        <?php
    }
}
add_action('admin_footer', 'qeditor_enqueue_admin_assets');

/**
 * Add ACE Editor UI and functionality.
 */
function qeditor_add_ace_editor()
{
    ?>
    <style>
        #ace-container-parent {
            display: none;
            width: 100%;
            gap: 10px;
            flex-wrap: nowrap;
        }

        #ace-editor-container {
            flex: 2;
            height: 500px;
            border: 1px solid #c3c4c7;
            background: #1e1e1e;
        }

        #ace-live-preview {
            flex: 1;
            min-width: 300px;
            border: 1px solid #c3c4c7;
            background: #fff;
            overflow: auto;
            padding: 10px;
        }

        #ace-toolbar {
            background: #f0f0f1;
            border: 1px solid #c3c4c7;
            padding: 10px;
            display: none;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        #ace-toolbar select,
        #ace-toolbar button {
            margin-right: 0;
        }

        #ace-toolbar .toolbar-group {
            display: flex;
            align-items: center;
            gap: 5px;
            border-right: 1px solid #c3c4c7;
            padding-right: 10px;
        }

        #ace-toolbar .toolbar-group:last-child {
            border-right: none;
            padding-right: 0;
        }

        #ace-toolbar label {
            font-size: 12px;
            font-weight: 600;
            color: #555;
        }

        #ace-toolbar select {
            min-width: 120px;
        }

        #preview-context-info {
            font-size: 11px;
            color: #666;
            font-style: italic;
        }

        #ace-statusbar {
            background: #222;
            color: #aaa;
            font-size: 12px;
            padding: 5px;
            display: none;
        }
    </style>


    <script>
        jQuery(document).ready(function ($) {
            if (typeof ace === 'undefined') {
                var aceScript = document.createElement('script');
                aceScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/ace/1.23.1/ace.js';
                document.head.appendChild(aceScript);
            }

            var aceTab = $('<button>', {
                type: 'button',
                id: 'content-qeditor',
                class: 'wp-switch-editor switch-qeditor',
                text: 'Q-Editor'
            });
            $('.wp-editor-tabs').append(aceTab);

            var aceContainerParent = $('<div>', { id: 'ace-container-parent', style: 'display: flex;' }).insertAfter('#wp-content-editor-container');
            var aceContainer = $('<div>', { id: 'ace-editor-container' }).appendTo(aceContainerParent);
            var previewPane = $('<div>', { id: 'ace-live-preview' }).appendTo(aceContainerParent);

            var aceToolbar = $('<div>', {
                id: 'ace-toolbar',
                html: `
                <div class="toolbar-group">
                    <button id="beautify-code">Beautify</button>
                    <button id="live-preview">Toggle Preview</button>
                </div>
                <div class="toolbar-group">
                    <label for="preview-form-select">Form:</label>
                    <select id="preview-form-select">
                        <option value="">Select Form...</option>
                    </select>
                    <label for="preview-entry-select">Entry:</label>
                    <select id="preview-entry-select">
                        <option value="">Select Entry...</option>
                        <option value="sample">Use Sample Data</option>
                    </select>
                </div>
                <div class="toolbar-group">
                    <button id="refresh-preview">Refresh Preview</button>
                    <span id="preview-context-info"></span>
                </div>
            `
            }).insertBefore(aceContainerParent);

            var statusBar = $('<div>', { id: 'ace-statusbar' }).insertAfter(aceContainerParent);

            var textarea = $('#content');
            var aceEditor;
            var previewVisible = false;
            var selectedFormId = '';
            var selectedEntryId = '';

            // Load forms on initialization
            function loadForms() {
                $.post(ajaxurl, {
                    action: 'qeditor_get_forms'
                }, function (response) {
                    if (response.success && response.data) {
                        var formSelect = $('#preview-form-select');
                        formSelect.empty().append('<option value="">Select Form...</option>');
                        $.each(response.data, function (id, name) {
                            formSelect.append('<option value="' + id + '">' + name + '</option>');
                        });
                    }
                });
            }

            // Load entries for selected form
            function loadEntries(formId) {
                if (!formId) {
                    $('#preview-entry-select').empty().append('<option value="">Select Entry...</option><option value="sample">Use Sample Data</option>');
                    return;
                }

                $.post(ajaxurl, {
                    action: 'qeditor_get_entries',
                    form_id: formId
                }, function (response) {
                    var entrySelect = $('#preview-entry-select');
                    entrySelect.empty().append('<option value="">Select Entry...</option><option value="sample">Use Sample Data</option>');

                    if (response.success && response.data) {
                        $.each(response.data, function (id, title) {
                            entrySelect.append('<option value="' + id + '">' + title + '</option>');
                        });
                    }
                });
            }

            function initAceEditor() {
                if (!aceEditor) {
                    aceEditor = ace.edit('ace-editor-container');
                    aceEditor.setOptions({
                        fontSize: '14px',
                        wrap: true, // Always wrapped
                        showPrintMargin: false
                    });

                    // For autocompletion:
                    ace.config.loadModule('ace/ext/language_tools', function () {
                        aceEditor.setOptions({
                            enableBasicAutocompletion: true,
                            enableSnippets: true,
                            enableLiveAutocompletion: true
                        });
                    });

                    // Set fixed HTML mode
                    aceEditor.setTheme('ace/theme/monokai');
                    aceEditor.session.setMode('ace/mode/html');

                    aceEditor.getSession().selection.on('changeCursor', function () {
                        var pos = aceEditor.getCursorPosition();
                        statusBar.text('Line: ' + (pos.row + 1) + ', Column: ' + (pos.column + 1));
                    });
                }
                aceEditor.setValue(textarea.val(), -1);
                aceEditor.focus();
                updatePreview();
            }

            $('#beautify-code').on('click', function () {
                var code = aceEditor.getValue();
                if (window.html_beautify) {
                    aceEditor.setValue(html_beautify(code), -1);
                }
                updatePreview();
            });

            $('#live-preview').on('click', function () {
                previewVisible = !previewVisible;
                if (previewVisible) {
                    previewPane.show();
                    updatePreview();
                } else {
                    previewPane.hide();
                }
            });

            function updatePreview() {
                if (previewVisible) {
                    var code = aceEditor.getValue();
                    previewPane.html('<em>Loading preview...</em>');

                    // Update context info
                    var contextInfo = '';
                    if (selectedFormId && selectedEntryId) {
                        if (selectedEntryId === 'sample') {
                            contextInfo = 'Using sample data for Form #' + selectedFormId;
                        } else {
                            contextInfo = 'Using Entry #' + selectedEntryId + ' from Form #' + selectedFormId;
                        }
                    } else {
                        contextInfo = 'No entry context - shortcodes may show empty values';
                    }
                    $('#preview-context-info').text(contextInfo);

                    $.post(ajaxurl, {
                        action: 'qeditor_render_preview',
                        content: code,
                        form_id: selectedFormId,
                        entry_id: selectedEntryId
                    }, function (response) {
                        if (response.success) {
                            previewPane.html(response.data);
                        } else {
                            previewPane.html('<em>Error rendering preview: ' + (response.data || 'Unknown error') + '</em>');
                        }
                    });
                }
            }

            $('.wp-switch-editor').on('click', function () {
                var activeTabId = $(this).attr('id');

                if ($('#content-qeditor').hasClass('active') && aceEditor) {
                    textarea.val(aceEditor.getValue());
                }

                $('.wp-switch-editor').removeClass('active').removeAttr('aria-pressed');
                $(this).addClass('active').attr('aria-pressed', 'true');

                if (activeTabId === 'content-html' || activeTabId === 'content-tmce') {
                    textarea.show();
                    aceContainerParent.hide();
                    aceToolbar.hide();
                    statusBar.hide();
                } else if (activeTabId === 'content-qeditor') {
                    textarea.hide();
                    aceContainerParent.show();
                    aceToolbar.show();
                    statusBar.show();
                    initAceEditor();
                }
            });

            $('form').on('submit', function () {
                if ($('#content-qeditor').attr('aria-pressed') === 'true' && aceEditor) {
                    textarea.val(aceEditor.getValue());
                }
            });

            // Event handlers for form/entry selection
            $('#preview-form-select').on('change', function () {
                selectedFormId = $(this).val();
                selectedEntryId = '';
                $('#preview-entry-select').val('');
                loadEntries(selectedFormId);
                updatePreview();
            });

            $('#preview-entry-select').on('change', function () {
                selectedEntryId = $(this).val();
                updatePreview();
            });

            $('#refresh-preview').on('click', function () {
                updatePreview();
            });

            // Initialize forms loading
            loadForms();

            // Load beautify library
            var beautifyScript = document.createElement('script');
            beautifyScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.7/beautify-html.min.js';
            document.head.appendChild(beautifyScript);
            var beautifyScript2 = document.createElement('script');
            beautifyScript2.src = 'https://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.7/beautify.min.js';
            document.head.appendChild(beautifyScript2);
        });
    </script>
    <?php
}
add_action('admin_footer', 'qeditor_add_ace_editor');

/**
 * Ajax handler for rendering shortcodes in live preview with entry context.
 */
function qeditor_render_live_preview()
{
    if (!current_user_can('edit_posts')) {
        wp_send_json_error('Unauthorized');
    }

    if (!isset($_POST['content'])) {
        wp_send_json_error('No content');
        return;
    }

    $content = wp_unslash($_POST['content']);
    $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;
    $entry_id = isset($_POST['entry_id']) ? sanitize_text_field($_POST['entry_id']) : '';

    // Set up entry context for Formidable Forms shortcodes
    if ($form_id && $entry_id && class_exists('FrmEntry')) {
        if ($entry_id === 'sample') {
            // Use sample data
            qeditor_setup_sample_data($form_id);
        } else {
            // Use real entry data
            $entry = FrmEntry::getOne($entry_id);
            if ($entry && $entry->form_id == $form_id) {
                // Set global entry context for shortcode processing
                global $frm_vars;
                if (!isset($frm_vars)) {
                    $frm_vars = array();
                }
                $frm_vars['entry_id'] = $entry_id;
                $frm_vars['form_id'] = $form_id;
            }
        }
    }

    $processed = do_shortcode($content);
    wp_send_json_success($processed);
}
add_action('wp_ajax_qeditor_render_preview', 'qeditor_render_live_preview');

/**
 * Ajax handler for getting available forms.
 */
function qeditor_get_forms()
{
    if (!current_user_can('edit_posts')) {
        wp_send_json_error('Unauthorized');
    }

    $forms = array();

    if (class_exists('FrmForm')) {
        $frm_forms = FrmForm::get_published_forms();
        foreach ($frm_forms as $form) {
            $forms[$form->id] = $form->name . ' (ID: ' . $form->id . ')';
        }
    }

    wp_send_json_success($forms);
}
add_action('wp_ajax_qeditor_get_forms', 'qeditor_get_forms');

/**
 * Ajax handler for getting entries for a specific form.
 */
function qeditor_get_entries()
{
    if (!current_user_can('edit_posts')) {
        wp_send_json_error('Unauthorized');
    }

    $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;
    if (!$form_id) {
        wp_send_json_error('No form ID provided');
    }

    $entries = array();

    if (class_exists('FrmEntry')) {
        $frm_entries = FrmEntry::getAll(array('form_id' => $form_id), ' ORDER BY created_at DESC LIMIT 20');
        foreach ($frm_entries as $entry) {
            $title = 'Entry #' . $entry->id;
            if (!empty($entry->name)) {
                $title .= ' - ' . $entry->name;
            }
            $title .= ' (' . date('M j, Y', strtotime($entry->created_at)) . ')';
            $entries[$entry->id] = $title;
        }
    }

    wp_send_json_success($entries);
}
add_action('wp_ajax_qeditor_get_entries', 'qeditor_get_entries');

/**
 * Set up sample data for preview when using "sample" entry option.
 */
function qeditor_setup_sample_data($form_id)
{
    if (!class_exists('FrmField') || !class_exists('FrmProEntriesController')) {
        return;
    }

    // Get form fields
    $fields = FrmField::get_all_for_form($form_id);

    // Create sample data based on field types
    $sample_data = array();
    foreach ($fields as $field) {
        $sample_value = qeditor_get_sample_field_value($field);
        if ($sample_value !== null) {
            $sample_data[$field->id] = $sample_value;
        }
    }

    // Set up global variables for Formidable shortcode processing
    global $frm_vars;
    if (!isset($frm_vars)) {
        $frm_vars = array();
    }
    $frm_vars['sample_data'] = $sample_data;
    $frm_vars['form_id'] = $form_id;
    $frm_vars['entry_id'] = 'sample';

    // Hook into Formidable's field value retrieval to provide sample data
    add_filter('frm_get_field_value_shortcode', 'qeditor_filter_field_value_for_sample', 10, 2);
}

/**
 * Generate sample value based on field type.
 */
function qeditor_get_sample_field_value($field)
{
    switch ($field->type) {
        case 'text':
        case 'textarea':
            return 'Sample ' . ucfirst($field->type) . ' Value';
        case 'email':
            return '<EMAIL>';
        case 'url':
            return 'https://example.com';
        case 'phone':
            return '(*************';
        case 'number':
            return '42';
        case 'date':
            return date('Y-m-d');
        case 'time':
            return date('H:i');
        case 'select':
        case 'radio':
        case 'checkbox':
            // Get first option if available
            if (!empty($field->options)) {
                $options = maybe_unserialize($field->options);
                if (is_array($options) && !empty($options)) {
                    return array_keys($options)[0];
                }
            }
            return 'Sample Option';
        case 'user_id':
            return get_current_user_id();
        default:
            return 'Sample Value';
    }
}

/**
 * Filter field values to provide sample data when in sample mode.
 */
function qeditor_filter_field_value_for_sample($value, $atts)
{
    global $frm_vars;

    if (isset($frm_vars['entry_id']) && $frm_vars['entry_id'] === 'sample' && isset($frm_vars['sample_data'])) {
        $field_id = isset($atts['field_id']) ? $atts['field_id'] : '';
        if ($field_id && isset($frm_vars['sample_data'][$field_id])) {
            return $frm_vars['sample_data'][$field_id];
        }
    }

    return $value;
}
