<?php
/**
 * Plugin Name: Q-Editor
 * Description: Adds ACE Editor with Live Preview (processing shortcodes) to the Formidable Views editor.
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL2
 * Text Domain: q-editor
 */

if (!defined('ABSPATH'))
    exit;

/**
 * Enqueue Q-Editor scripts and styles in the admin.
 */
function qeditor_enqueue_admin_assets($hook)
{
    // Only load on post editing screens (where Formidable Views editor lives).
    if (in_array($hook, ['post-new.php', 'post.php'])) {
        // Add ajaxurl global.
        ?>
        <script>var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';</script>
        <?php
    }
}
add_action('admin_footer', 'qeditor_enqueue_admin_assets');

/**
 * Add ACE Editor UI and functionality.
 */
function qeditor_add_ace_editor()
{
    ?>
    <style>
        #ace-container-parent {
            display: none;
            width: 100%;
            gap: 10px;
            flex-wrap: nowrap;
        }

        #ace-editor-container {
            flex: 2;
            height: 500px;
            border: 1px solid #c3c4c7;
            background: #1e1e1e;
        }

        #ace-live-preview {
            flex: 1;
            min-width: 300px;
            border: 1px solid #c3c4c7;
            background: #fff;
            overflow: auto;
            padding: 10px;
        }

        #ace-toolbar {
            background: #f0f0f1;
            border: 1px solid #c3c4c7;
            padding: 5px;
            display: none;
            margin-bottom: 5px;
        }

        #ace-toolbar select,
        #ace-toolbar button {
            margin-right: 10px;
        }

        #ace-statusbar {
            background: #222;
            color: #aaa;
            font-size: 12px;
            padding: 5px;
            display: none;
        }
    </style>

    
    <script>
        jQuery(document).ready(function ($) {
            if (typeof ace === 'undefined') {
                var aceScript = document.createElement('script');
                aceScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/ace/1.23.1/ace.js';
                document.head.appendChild(aceScript);
            }

            var aceTab = $('<button>', {
                type: 'button',
                id: 'content-qeditor',
                class: 'wp-switch-editor switch-qeditor',
                text: 'Q-Editor'
            });
            $('.wp-editor-tabs').append(aceTab);

            var aceContainerParent = $('<div>', { id: 'ace-container-parent', style: 'display: flex;' }).insertAfter('#wp-content-editor-container');
            var aceContainer = $('<div>', { id: 'ace-editor-container' }).appendTo(aceContainerParent);
            var previewPane = $('<div>', { id: 'ace-live-preview' }).appendTo(aceContainerParent);

            var aceToolbar = $('<div>', {
                id: 'ace-toolbar',
                html: `
                <button id="beautify-code">Beautify</button>
                <button id="live-preview">Toggle Preview</button>
            `
            }).insertBefore(aceContainerParent);

            var statusBar = $('<div>', { id: 'ace-statusbar' }).insertAfter(aceContainerParent);

            var textarea = $('#content');
            var aceEditor;
            var previewVisible = false;

            function initAceEditor() {
                if (!aceEditor) {
                    aceEditor = ace.edit('ace-editor-container');
                    aceEditor.setOptions({
                        fontSize: '14px',
                        wrap: true, // Always wrapped
                        showPrintMargin: false
                    });

                    // For autocompletion:
                    ace.config.loadModule('ace/ext/language_tools', function () {
                        aceEditor.setOptions({
                            enableBasicAutocompletion: true,
                            enableSnippets: true,
                            enableLiveAutocompletion: true
                        });
                    });

                    // Set fixed HTML mode
                    aceEditor.setTheme('ace/theme/monokai');
                    aceEditor.session.setMode('ace/mode/html');

                    aceEditor.getSession().selection.on('changeCursor', function () {
                        var pos = aceEditor.getCursorPosition();
                        statusBar.text('Line: ' + (pos.row + 1) + ', Column: ' + (pos.column + 1));
                    });
                }
                aceEditor.setValue(textarea.val(), -1);
                aceEditor.focus();
                updatePreview();
            }

            $('#beautify-code').on('click', function () {
                var code = aceEditor.getValue();
                if (window.html_beautify) {
                    aceEditor.setValue(html_beautify(code), -1);
                }
                updatePreview();
            });

            $('#live-preview').on('click', function () {
                previewVisible = !previewVisible;
                if (previewVisible) {
                    previewPane.show();
                    updatePreview();
                } else {
                    previewPane.hide();
                }
            });

            function updatePreview() {
                if (previewVisible) {
                    var code = aceEditor.getValue();
                    previewPane.html('<em>Loading preview...</em>');
                    $.post(ajaxurl, {
                        action: 'qeditor_render_preview',
                        content: code
                    }, function (response) {
                        if (response.success) {
                            previewPane.html(response.data);
                        } else {
                            previewPane.html('<em>Error rendering preview</em>');
                        }
                    });
                }
            }

            $('.wp-switch-editor').on('click', function () {
                var activeTabId = $(this).attr('id');

                if ($('#content-qeditor').hasClass('active') && aceEditor) {
                    textarea.val(aceEditor.getValue());
                }

                $('.wp-switch-editor').removeClass('active').removeAttr('aria-pressed');
                $(this).addClass('active').attr('aria-pressed', 'true');

                if (activeTabId === 'content-html' || activeTabId === 'content-tmce') {
                    textarea.show();
                    aceContainerParent.hide();
                    aceToolbar.hide();
                    statusBar.hide();
                } else if (activeTabId === 'content-qeditor') {
                    textarea.hide();
                    aceContainerParent.show();
                    aceToolbar.show();
                    statusBar.show();
                    initAceEditor();
                }
            });

            $('form').on('submit', function () {
                if ($('#content-qeditor').attr('aria-pressed') === 'true' && aceEditor) {
                    textarea.val(aceEditor.getValue());
                }
            });

            // Load beautify library
            var beautifyScript = document.createElement('script');
            beautifyScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.7/beautify-html.min.js';
            document.head.appendChild(beautifyScript);
            var beautifyScript2 = document.createElement('script');
            beautifyScript2.src = 'https://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.7/beautify.min.js';
            document.head.appendChild(beautifyScript2);
        });
    </script>
    <?php
}
add_action('admin_footer', 'qeditor_add_ace_editor');

/**
 * Ajax handler for rendering shortcodes in live preview.
 */
function qeditor_render_live_preview()
{
    if (!current_user_can('edit_posts')) {
        wp_send_json_error('Unauthorized');
    }
    if (isset($_POST['content'])) {
        $content = wp_unslash($_POST['content']);
        $processed = do_shortcode($content);
        wp_send_json_success($processed);
    } else {
        wp_send_json_error('No content');
    }
}
add_action('wp_ajax_qeditor_render_preview', 'qeditor_render_live_preview');
